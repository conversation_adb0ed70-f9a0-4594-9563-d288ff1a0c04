import { ref } from 'vue'
import { message } from 'ant-design-vue'
import adminLangchainService from '@/services/adminLangchainService'
import knowledgeBaseService from '@/services/knowledgeBaseService'

/**
 * 智能体API调用逻辑
 */
export function useAgentAPI() {
  
  // 基础数据
  const 对话模型列表 = ref([])
  const 嵌入模型列表 = ref([])
  const 可用知识库列表 = ref([])
  const 可用工具列表 = ref([])
  
  // 加载状态
  const 加载状态 = ref({
    模型列表: false,
    知识库列表: false,
    工具列表: false,
    智能体数据: false
  })

  // 加载对话模型列表
  const 加载对话模型列表 = async () => {
    try {
      加载状态.value.模型列表 = true
      const 响应 = await adminLangchainService.getModelList('chat')

      if (响应.success) {
        对话模型列表.value = 响应.data.map(模型 => ({
          label: 模型.显示名称 || 模型.模型名称,
          value: 模型.id,
          ...模型
        }))
      } else {
        message.error(响应.error)
      }
    } catch (error) {
      console.error('加载对话模型列表失败:', error)
      message.error('加载对话模型列表失败')
    } finally {
      加载状态.value.模型列表 = false
    }
  }

  // 加载嵌入模型列表
  const 加载嵌入模型列表 = async () => {
    try {
      const 响应 = await adminLangchainService.getModelList('embedding')

      if (响应.success) {
        嵌入模型列表.value = 响应.data.map(模型 => ({
          label: 模型.显示名称 || 模型.模型名称,
          value: 模型.id,
          ...模型
        }))
      } else {
        message.error(响应.error)
      }
    } catch (error) {
      console.error('加载嵌入模型列表失败:', error)
      message.error('加载嵌入模型列表失败')
    }
  }

  // 加载知识库列表
  const 加载知识库列表 = async () => {
    try {
      加载状态.value.知识库列表 = true
      const 响应 = await knowledgeBaseService.getKnowledgeBaseList()

      if (响应.success) {
        可用知识库列表.value = (响应.data.知识库列表 || []).map(知识库 => ({
          label: 知识库.知识库名称,
          value: 知识库.id,
          ...知识库
        }))
      } else {
        message.error(响应.error)
      }
    } catch (error) {
      console.error('加载知识库列表失败:', error)
      message.error('加载知识库列表失败')
    } finally {
      加载状态.value.知识库列表 = false
    }
  }

  // 加载可用工具列表
  const 加载可用工具列表 = async () => {
    try {
      加载状态.value.工具列表 = true
      const 响应 = await adminLangchainService.获取工具列表()

      if (响应.success) {
        可用工具列表.value = 响应.data || []
      } else {
        message.error(响应.message)
      }
    } catch (error) {
      console.error('加载工具列表失败:', error)
      message.error('加载工具列表失败')
    } finally {
      加载状态.value.工具列表 = false
    }
  }

  // 加载智能体数据
  const 加载智能体数据 = async (智能体ID) => {
    try {
      加载状态.value.智能体数据 = true
      const 响应 = await adminLangchainService.getAgentDetail(智能体ID)

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.error || '获取智能体数据失败')
      }
    } catch (error) {
      console.error('加载智能体数据失败:', error)
      message.error(error.message || '加载智能体数据失败')
      throw error
    } finally {
      加载状态.value.智能体数据 = false
    }
  }

  // 保存智能体
  const 保存智能体 = async (智能体数据, 是否编辑模式 = false, 智能体ID = null) => {
    try {
      // 字段映射：前端字段 -> 后端字段
      const 后端数据 = {
        ...智能体数据,
        // 模型配置映射
        langchain_模型配置表id: 智能体数据.对话模型ID,
        模型名称: null, // 后端会根据langchain_模型配置表id自动获取

        // 输出配置映射
        输出格式: 智能体数据.输出模式 === 'structured' ? 'json' : 'text',
        自定义回复格式: 智能体数据.输出模式 === 'structured' ? 智能体数据.json_schema : null,

        // RAG配置重新组装
        rag_配置: {
          检索策略: 智能体数据.检索策略 || 'similarity',
          嵌入模型ID: 智能体数据.嵌入模型ID,
          相似度阈值: 智能体数据.相似度阈值 || 0.7,
          最大检索数量: 智能体数据.最大检索数量 || 5
        },

        // 移除前端专用字段
        对话模型ID: undefined,
        输出模式: undefined,
        json_schema: undefined,
        检索策略: undefined,
        嵌入模型ID: undefined,
        相似度阈值: undefined,
        最大检索数量: undefined,
        启用工具调用: undefined
      }

      console.log('💾 保存智能体数据映射:', {
        前端数据: 智能体数据,
        后端数据: 后端数据
      })

      let 响应

      if (是否编辑模式 && 智能体ID) {
        响应 = await adminLangchainService.updateAgent(智能体ID, 后端数据)
      } else {
        响应 = await adminLangchainService.createAgent(后端数据)
      }

      if (响应.success) {
        message.success(是否编辑模式 ? '智能体更新成功' : '智能体创建成功')
        return 响应.data
      } else {
        throw new Error(响应.error || '保存智能体失败')
      }
    } catch (error) {
      console.error('保存智能体失败:', error)
      message.error(error.message || '保存智能体失败')
      throw error
    }
  }

  // 测试智能体对话
  const 测试智能体对话 = async (智能体ID, 消息, 选项 = {}) => {
    try {
      const 响应 = await adminLangchainService.chatWithAgent(智能体ID, {
        消息: 消息,
        保留历史: 选项.保留历史 ?? true,
        调试模式: 选项.调试模式 ?? false
      })

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.error || '对话测试失败')
      }
    } catch (error) {
      console.error('测试智能体对话失败:', error)
      message.error(error.message || '对话测试失败')
      throw error
    }
  }

  // 测试RAG检索
  const 测试RAG检索 = async (智能体ID, 查询文本, 检索参数 = {}) => {
    try {
      const 响应 = await adminLangchainService.testAgentRetrieval(智能体ID, {
        查询文本: 查询文本,
        检索参数: 检索参数
      })

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.error || 'RAG检索测试失败')
      }
    } catch (error) {
      console.error('测试RAG检索失败:', error)
      message.error(error.message || 'RAG检索测试失败')
      throw error
    }
  }

  // 获取智能体工具配置
  const 获取智能体工具配置 = async (智能体ID) => {
    try {
      const 响应 = await adminLangchainService.获取智能体工具关联(智能体ID)

      if (响应.success) {
        return 响应.data
      } else {
        throw new Error(响应.message || '获取工具配置失败')
      }
    } catch (error) {
      console.error('获取智能体工具配置失败:', error)
      throw error
    }
  }

  // 更新智能体工具配置
  const 更新智能体工具配置 = async (智能体ID, 工具配置) => {
    try {
      const 响应 = await adminLangchainService.保存智能体工具关联(智能体ID, 工具配置)

      if (响应.success) {
        message.success('工具配置更新成功')
        return 响应.data
      } else {
        throw new Error(响应.message || '更新工具配置失败')
      }
    } catch (error) {
      console.error('更新智能体工具配置失败:', error)
      message.error(error.message || '更新工具配置失败')
      throw error
    }
  }

  // 初始化基础数据
  const 初始化基础数据 = async () => {
    try {
      await Promise.all([
        加载对话模型列表(),
        加载嵌入模型列表(),
        加载知识库列表(),
        加载可用工具列表()
      ])
    } catch (error) {
      console.error('初始化基础数据失败:', error)
      message.error('初始化基础数据失败，请刷新页面重试')
      throw error
    }
  }

  return {
    // 数据
    对话模型列表,
    嵌入模型列表,
    可用知识库列表,
    可用工具列表,
    加载状态,
    
    // 方法
    加载对话模型列表,
    加载嵌入模型列表,
    加载知识库列表,
    加载可用工具列表,
    加载智能体数据,
    保存智能体,
    测试智能体对话,
    测试RAG检索,
    获取智能体工具配置,
    更新智能体工具配置,
    初始化基础数据
  }
}
